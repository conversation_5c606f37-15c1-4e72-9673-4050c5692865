import { app, shell, BrowserWindow } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'

// 全局主窗口引用，用于单例模式
let mainWindow: BrowserWindow | null = null

function createWindow(): void {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 基于electron-vite cli的渲染器热更新
  // 开发环境下加载远程URL，生产环境下加载本地HTML文件
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// 单例模式实现：确保应用程序只能运行一个实例
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  // 如果无法获取单例锁，说明已有实例在运行，退出当前实例
  app.quit()
} else {
  // 当第二个实例尝试启动时，聚焦到已存在的窗口
  app.on('second-instance', () => {
    // 如果主窗口存在，则聚焦并显示它
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore()
      }
      mainWindow.focus()

      mainWindow.setAlwaysOnTop(true)
      mainWindow.setAlwaysOnTop(false)
    }
  })

  // 当Electron完成初始化并准备创建浏览器窗口时调用此方法
  // 某些API只能在此事件发生后使用
  app.whenReady().then(() => {
    // 为Windows设置应用程序用户模型ID
    electronApp.setAppUserModelId('com.aikairo.tbaw')

    // 在开发环境中通过F12默认打开或关闭开发者工具
    // 在生产环境中忽略CommandOrControl + R
    // 详见 https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
    app.on('browser-window-created', (_, window) => {
      optimizer.watchWindowShortcuts(window)
    })

    createWindow()

    app.on('activate', function () {
      // 在macOS上，当点击dock图标且没有其他窗口打开时，
      // 通常会在应用程序中重新创建一个窗口
      if (BrowserWindow.getAllWindows().length === 0) createWindow()
    })
  })

  // 当所有窗口都被关闭时退出应用，除了在macOS上
  // 在macOS上，应用及其菜单栏通常会保持活动状态，
  // 直到用户使用Cmd + Q明确退出
  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit()
    }
  })

  // 在这个文件中，你可以包含应用程序特定主进程的其余代码。
  // 你也可以将它们放在单独的文件中，并在这里引入它们。
}
