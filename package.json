{"name": "tbaw", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "<PERSON><PERSON><PERSON>", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev -- --trace-warnings", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps && electron-rebuild", "rebuild": "electron-rebuild", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@ai-sdk/react": "^1.2.12", "@ariakit/react": "^0.4.17", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@emoji-mart/data": "^1.2.1", "@faker-js/faker": "^9.8.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toolbar": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.8", "@udecode/cn": "^48.0.3", "@udecode/plate": "^48.0.5", "@udecode/plate-ai": "^48.0.5", "@udecode/plate-alignment": "^48.0.0", "@udecode/plate-autoformat": "^48.0.0", "@udecode/plate-basic-marks": "^48.0.0", "@udecode/plate-block-quote": "^48.0.0", "@udecode/plate-break": "^48.0.0", "@udecode/plate-callout": "^48.0.0", "@udecode/plate-caption": "^48.0.0", "@udecode/plate-code-block": "^48.0.0", "@udecode/plate-combobox": "^48.0.0", "@udecode/plate-comments": "^48.0.0", "@udecode/plate-date": "^48.0.0", "@udecode/plate-dnd": "^48.0.0", "@udecode/plate-docx": "^48.0.6", "@udecode/plate-emoji": "^48.0.0", "@udecode/plate-excalidraw": "^48.0.0", "@udecode/plate-floating": "^48.0.0", "@udecode/plate-font": "^48.0.0", "@udecode/plate-heading": "^48.0.0", "@udecode/plate-highlight": "^48.0.0", "@udecode/plate-horizontal-rule": "^48.0.0", "@udecode/plate-indent": "^48.0.0", "@udecode/plate-indent-list": "^48.0.0", "@udecode/plate-juice": "^48.0.0", "@udecode/plate-kbd": "^48.0.0", "@udecode/plate-layout": "^48.0.0", "@udecode/plate-line-height": "^48.0.0", "@udecode/plate-link": "^48.0.0", "@udecode/plate-markdown": "^48.0.2", "@udecode/plate-math": "^48.0.4", "@udecode/plate-media": "^48.0.0", "@udecode/plate-mention": "^48.0.0", "@udecode/plate-node-id": "^48.0.6", "@udecode/plate-reset-node": "^48.0.0", "@udecode/plate-resizable": "^48.0.0", "@udecode/plate-select": "^48.0.0", "@udecode/plate-selection": "^48.0.5", "@udecode/plate-slash-command": "^48.0.0", "@udecode/plate-suggestion": "^48.0.0", "@udecode/plate-table": "^48.0.6", "@udecode/plate-toggle": "^48.0.6", "@udecode/plate-trailing-block": "^48.0.0", "@uploadthing/react": "^7.3.1", "ai": "^4.3.16", "better-sqlite3": "^11.10.0", "bullmq": "^5.53.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "electron-updater": "^6.3.9", "html2canvas-pro": "^1.5.11", "immer": "^10.1.1", "ioredis": "^5.6.1", "lowlight": "^3.3.0", "lucide-react": "^0.511.0", "pdf-lib": "^1.17.1", "postcss": "^8.5.4", "react-day-picker": "^9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-lite-youtube-embed": "^2.5.1", "react-player": "^2.16.0", "react-textarea-autosize": "^8.5.9", "react-tweet": "^3.2.2", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwind-scrollbar-hide": "^4.0.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.3", "uploadthing": "^7.7.2", "use-file-picker": "^2.1.4", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/better-sqlite3": "^7.6.13", "@types/node": "^22.15.29", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-rebuild": "^3.2.9", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^6.2.6"}}